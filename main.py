"""Main entry point for funding arbitrage data feeds."""

import asyncio
import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.feed_manager import FeedManager


async def main():
    """Main function to run the data feed manager."""
    print("Starting Funding Arbitrage Data Feeds with TimescaleDB...")

    # Create and run feed manager
    manager = FeedManager()

    try:
        await manager.run_forever()
    except Exception as e:
        print(f"Error running feed manager: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
