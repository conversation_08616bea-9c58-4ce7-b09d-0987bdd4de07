#!/usr/bin/env python3
"""Initialize TimescaleDB database schema for funding arbitrage bot."""

import asyncio
import asyncpg
import sys
import os
from typing import Optional

# Database connection settings
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'user': 'postgres',
    'password': 'fundingarb',
    'database': 'postgres'  # Connect to default database first
}

FUNDINGARB_DB = 'fundingarb'

# SQL to create the database
CREATE_DATABASE_SQL = f"""
CREATE DATABASE {FUNDINGARB_DB};
"""

# SQL to drop and recreate the funding_rates table
DROP_TABLE_SQL = """
DROP TABLE IF EXISTS funding_rates CASCADE;
"""

# SQL to create the funding_rates table
CREATE_TABLE_SQL = """
CREATE TABLE funding_rates (
    timestamp TIMESTAMPTZ NOT NULL,
    exchange VARCHAR(50) NOT NULL,
    instrument VARCHAR(100) NOT NULL,
    funding_rate DECIMAL(20, 15) NOT NULL,
    funding_time BIGINT,
    collection_time DECIMAL(20, 10),
    open_interest DECIMAL(30, 8),
    prev_day_px DECIMAL(20, 8),
    day_ntl_vlm DECIMAL(30, 8),
    premium DECIMAL(20, 8),
    oracle_px DECIMAL(20, 8),
    mark_px DECIMAL(20, 8),
    mid_px DECIMAL(20, 8),
    day_base_vlm DECIMAL(30, 8),
    raw_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (timestamp, exchange, instrument)
);
"""

# SQL to create hypertable (TimescaleDB specific)
CREATE_HYPERTABLE_SQL = """
SELECT create_hypertable('funding_rates', 'timestamp', if_not_exists => TRUE);
"""

# SQL to create indexes for better query performance
CREATE_INDEXES_SQL = [
    "CREATE INDEX IF NOT EXISTS idx_funding_rates_exchange_instrument ON funding_rates (exchange, instrument);",
    "CREATE INDEX IF NOT EXISTS idx_funding_rates_timestamp ON funding_rates (timestamp DESC);",
    "CREATE INDEX IF NOT EXISTS idx_funding_rates_exchange_timestamp ON funding_rates (exchange, timestamp DESC);",
    "CREATE INDEX IF NOT EXISTS idx_funding_rates_instrument_timestamp ON funding_rates (instrument, timestamp DESC);",
]

# SQL to create a view for latest funding rates (similar to KeyDB functionality)
CREATE_LATEST_VIEW_SQL = """
CREATE OR REPLACE VIEW latest_funding_rates AS
SELECT DISTINCT ON (exchange, instrument)
    exchange,
    instrument,
    funding_rate,
    funding_time,
    collection_time,
    open_interest,
    prev_day_px,
    day_ntl_vlm,
    premium,
    oracle_px,
    mark_px,
    mid_px,
    day_base_vlm,
    raw_data,
    timestamp,
    created_at
FROM funding_rates
ORDER BY exchange, instrument, timestamp DESC;
"""

# SQL to create data retention policy (keep data for 7 days by default)
CREATE_RETENTION_POLICY_SQL = """
SELECT add_retention_policy('funding_rates', INTERVAL '7 days', if_not_exists => TRUE);
"""


async def database_exists(connection: asyncpg.Connection, db_name: str) -> bool:
    """Check if database exists."""
    result = await connection.fetchval(
        "SELECT 1 FROM pg_database WHERE datname = $1", db_name
    )
    return result is not None


async def create_database() -> bool:
    """Create the fundingarb database if it doesn't exist."""
    try:
        # Connect to default postgres database
        conn = await asyncpg.connect(**DB_CONFIG)
        
        # Check if fundingarb database exists
        if await database_exists(conn, FUNDINGARB_DB):
            print(f"Database '{FUNDINGARB_DB}' already exists.")
        else:
            print(f"Creating database '{FUNDINGARB_DB}'...")
            await conn.execute(CREATE_DATABASE_SQL)
            print(f"Database '{FUNDINGARB_DB}' created successfully.")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"Error creating database: {e}")
        return False


async def setup_schema() -> bool:
    """Set up the database schema in the fundingarb database."""
    try:
        # Connect to fundingarb database
        db_config = DB_CONFIG.copy()
        db_config['database'] = FUNDINGARB_DB
        
        conn = await asyncpg.connect(**db_config)

        print("Dropping existing funding_rates table if it exists...")
        await conn.execute(DROP_TABLE_SQL)

        print("Creating funding_rates table...")
        await conn.execute(CREATE_TABLE_SQL)
        
        print("Creating TimescaleDB hypertable...")
        await conn.execute(CREATE_HYPERTABLE_SQL)
        
        print("Creating indexes...")
        for index_sql in CREATE_INDEXES_SQL:
            await conn.execute(index_sql)
        
        print("Creating latest funding rates view...")
        await conn.execute(CREATE_LATEST_VIEW_SQL)
        
        print("Setting up data retention policy...")
        await conn.execute(CREATE_RETENTION_POLICY_SQL)
        
        await conn.close()
        print("Database schema setup completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error setting up schema: {e}")
        return False


async def test_connection() -> bool:
    """Test connection to the fundingarb database."""
    try:
        db_config = DB_CONFIG.copy()
        db_config['database'] = FUNDINGARB_DB
        
        conn = await asyncpg.connect(**db_config)
        
        # Test basic query
        result = await conn.fetchval("SELECT NOW()")
        print(f"Connection test successful. Current time: {result}")
        
        # Test table exists
        table_exists = await conn.fetchval(
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'funding_rates')"
        )
        print(f"funding_rates table exists: {table_exists}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"Connection test failed: {e}")
        return False


async def main():
    """Main function to initialize TimescaleDB."""
    print("Initializing TimescaleDB for funding arbitrage bot...")
    print("=" * 50)
    
    # Step 1: Create database
    if not await create_database():
        print("Failed to create database. Exiting.")
        return 1
    
    # Step 2: Setup schema
    if not await setup_schema():
        print("Failed to setup schema. Exiting.")
        return 1
    
    # Step 3: Test connection
    if not await test_connection():
        print("Connection test failed. Exiting.")
        return 1
    
    print("=" * 50)
    print("TimescaleDB initialization completed successfully!")
    print(f"Database: {FUNDINGARB_DB}")
    print("You can now run your funding arbitrage bot with TimescaleDB.")
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
