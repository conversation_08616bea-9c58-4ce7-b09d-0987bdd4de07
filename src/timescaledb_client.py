"""TimescaleDB client for storing funding rate data."""

import json
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
import asyncpg
from config import TimescaleDBConfig


logger = logging.getLogger(__name__)


class TimescaleDBClient:
    """Async TimescaleDB client for funding rate data storage."""
    
    def __init__(self, config: TimescaleDBConfig):
        self.config = config
        self.pool: Optional[asyncpg.Pool] = None
        self._connected = False
    
    async def connect(self) -> bool:
        """Connect to TimescaleDB."""
        try:
            self.pool = await asyncpg.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                database=self.config.database,
                min_size=self.config.min_connections,
                max_size=self.config.max_connections,
                command_timeout=self.config.command_timeout
            )
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            self._connected = True
            logger.info(f"Connected to TimescaleDB at {self.config.host}:{self.config.port}/{self.config.database}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to TimescaleDB: {e}")
            self._connected = False
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from TimescaleDB."""
        if self.pool:
            await self.pool.close()
            self._connected = False
            logger.info("Disconnected from TimescaleDB")
    
    async def store_funding_rate(self, exchange: str, instrument: str, data: Dict[str, Any]) -> bool:
        """
        Store funding rate data for an instrument.
        
        Args:
            exchange: Exchange name (e.g., 'blofin', 'hyperliquid')
            instrument: Instrument symbol (e.g., 'BTC-USDT', 'BTC')
            data: Funding rate data dictionary
            
        Returns:
            True if stored successfully
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return False
        
        try:
            # Extract timestamp or use current time
            timestamp = data.get('timestamp')
            if timestamp:
                # Convert Unix timestamp to datetime
                if isinstance(timestamp, (int, float)):
                    timestamp = datetime.fromtimestamp(timestamp, tz=timezone.utc)
                elif isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                timestamp = datetime.now(timezone.utc)
            
            # Prepare data for insertion
            insert_data = {
                'timestamp': timestamp,
                'exchange': exchange,
                'instrument': instrument,
                'funding_rate': data.get('funding_rate'),
                'funding_time': data.get('funding_time'),
                'collection_time': data.get('collection_time'),
                'open_interest': self._safe_decimal(data.get('open_interest')),
                'prev_day_px': self._safe_decimal(data.get('prev_day_px')),
                'day_ntl_vlm': self._safe_decimal(data.get('day_ntl_vlm')),
                'premium': self._safe_decimal(data.get('premium')),
                'oracle_px': self._safe_decimal(data.get('oracle_px')),
                'mark_px': self._safe_decimal(data.get('mark_px')),
                'mid_px': self._safe_decimal(data.get('mid_px')),
                'day_base_vlm': self._safe_decimal(data.get('day_base_vlm')),
                'raw_data': json.dumps(data.get('raw_data', {}))
            }
            
            # Insert into database (use ON CONFLICT to handle duplicates)
            async with self.pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO funding_rates (
                        timestamp, exchange, instrument, funding_rate, funding_time,
                        collection_time, open_interest, prev_day_px, day_ntl_vlm,
                        premium, oracle_px, mark_px, mid_px, day_base_vlm, raw_data
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
                    ) ON CONFLICT (timestamp, exchange, instrument) DO UPDATE SET
                        funding_rate = EXCLUDED.funding_rate,
                        funding_time = EXCLUDED.funding_time,
                        collection_time = EXCLUDED.collection_time,
                        open_interest = EXCLUDED.open_interest,
                        prev_day_px = EXCLUDED.prev_day_px,
                        day_ntl_vlm = EXCLUDED.day_ntl_vlm,
                        premium = EXCLUDED.premium,
                        oracle_px = EXCLUDED.oracle_px,
                        mark_px = EXCLUDED.mark_px,
                        mid_px = EXCLUDED.mid_px,
                        day_base_vlm = EXCLUDED.day_base_vlm,
                        raw_data = EXCLUDED.raw_data,
                        created_at = NOW()
                """, *insert_data.values())
            
            logger.debug(f"Stored funding rate for {exchange}:{instrument}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store funding rate for {exchange}:{instrument}: {e}")
            return False
    
    def _safe_decimal(self, value: Any) -> Optional[float]:
        """Safely convert value to decimal/float."""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    async def get_funding_rate(self, exchange: str, instrument: str) -> Optional[Dict[str, Any]]:
        """
        Get latest funding rate data for an instrument.
        
        Args:
            exchange: Exchange name
            instrument: Instrument symbol
            
        Returns:
            Funding rate data or None if not found
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return None
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT * FROM latest_funding_rates 
                    WHERE exchange = $1 AND instrument = $2
                """, exchange, instrument)
                
                if row:
                    return self._row_to_dict(row)
                return None
                
        except Exception as e:
            logger.error(f"Failed to get funding rate for {exchange}:{instrument}: {e}")
            return None
    
    async def get_all_funding_rates(self, exchange: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Get all latest funding rates, optionally filtered by exchange.
        
        Args:
            exchange: Optional exchange filter
            
        Returns:
            Dictionary of funding rates keyed by exchange:instrument
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return {}
        
        try:
            async with self.pool.acquire() as conn:
                if exchange:
                    rows = await conn.fetch("""
                        SELECT * FROM latest_funding_rates WHERE exchange = $1
                    """, exchange)
                else:
                    rows = await conn.fetch("SELECT * FROM latest_funding_rates")
                
                result = {}
                for row in rows:
                    data = self._row_to_dict(row)
                    key = f"{data['exchange']}:{data['instrument']}"
                    result[key] = data
                
                return result
                
        except Exception as e:
            logger.error(f"Failed to get all funding rates: {e}")
            return {}
    
    def _row_to_dict(self, row) -> Dict[str, Any]:
        """Convert database row to dictionary matching KeyDB format."""
        data = dict(row)
        
        # Convert timestamp to Unix timestamp for compatibility
        if data.get('timestamp'):
            data['timestamp'] = data['timestamp'].timestamp()
        
        # Parse raw_data JSON
        if data.get('raw_data'):
            try:
                data['raw_data'] = json.loads(data['raw_data'])
            except (json.JSONDecodeError, TypeError):
                data['raw_data'] = {}
        
        return data
    
    async def cleanup_expired_data(self) -> int:
        """
        Clean up expired funding rate data (older than retention period).
        Note: TimescaleDB handles this automatically with retention policies,
        but we provide this method for compatibility.
        
        Returns:
            Number of rows cleaned up (always 0 as TimescaleDB handles this)
        """
        if not self._connected or not self.pool:
            return 0
        
        try:
            # TimescaleDB handles cleanup automatically via retention policies
            # We could manually delete old data here if needed, but it's not necessary
            logger.debug("TimescaleDB handles data cleanup automatically via retention policies")
            return 0
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired data: {e}")
            return 0
    
    async def get_funding_rate_history(
        self, 
        exchange: str, 
        instrument: str, 
        hours: int = 24
    ) -> List[Dict[str, Any]]:
        """
        Get funding rate history for an instrument.
        
        Args:
            exchange: Exchange name
            instrument: Instrument symbol
            hours: Number of hours of history to retrieve
            
        Returns:
            List of funding rate data ordered by timestamp (newest first)
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return []
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT * FROM funding_rates 
                    WHERE exchange = $1 AND instrument = $2 
                    AND timestamp >= NOW() - INTERVAL '%s hours'
                    ORDER BY timestamp DESC
                """, exchange, instrument, hours)
                
                return [self._row_to_dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get funding rate history for {exchange}:{instrument}: {e}")
            return []
