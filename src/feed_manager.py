"""Data feed manager for coordinating multiple exchange feeds."""

import asyncio
import logging
import signal
from typing import Dict, List, Optional, Type
from base_feed import BaseFeed
from timescaledb_client import TimescaleDBClient
from config import Config, DataFeedConfig, BlofinConfig, HyperliquidConfig
from feeds import B<PERSON>finFeed, HyperliquidFeed


logger = logging.getLogger(__name__)


class FeedManager:
    """Manager for coordinating multiple data feeds."""
    
    # Registry of available feed classes
    FEED_CLASSES: Dict[str, Type[BaseFeed]] = {
        'blofin': BlofinFeed,
        'hyperliquid': HyperliquidFeed,
    }
    
    def __init__(self, config_path: str = "config/feeds.json"):
        self.config_manager = Config(config_path)
        self.config: Optional[DataFeedConfig] = None
        self.timescaledb_client: Optional[TimescaleDBClient] = None
        self.feeds: Dict[str, BaseFeed] = {}
        self._running = False
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/feeds.log', mode='a')
            ]
        )
        
        # Create logs directory if it doesn't exist
        import os
        os.makedirs('logs', exist_ok=True)
    
    async def initialize(self) -> bool:
        """Initialize the feed manager."""
        try:
            # Load configuration
            self.config = self.config_manager.load()
            
            # Set log level from config
            logging.getLogger().setLevel(self.config.log_level)
            
            # Initialize TimescaleDB client
            self.timescaledb_client = TimescaleDBClient(self.config.timescaledb)
            if not await self.timescaledb_client.connect():
                logger.error("Failed to connect to TimescaleDB")
                return False
            
            # Initialize feeds
            await self._initialize_feeds()
            
            logger.info("Feed manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize feed manager: {e}")
            return False
    
    async def _initialize_feeds(self) -> None:
        """Initialize all configured feeds."""
        if not self.config or not self.timescaledb_client:
            return
        
        for exchange_name, exchange_config in self.config.exchanges.items():
            if not exchange_config.enabled:
                logger.info(f"Skipping disabled exchange: {exchange_name}")
                continue
            
            feed_class = self.FEED_CLASSES.get(exchange_name)
            if not feed_class:
                logger.warning(f"No feed class found for exchange: {exchange_name}")
                continue
            
            try:
                # Create feed instance
                feed = feed_class(exchange_config, self.timescaledb_client)
                self.feeds[exchange_name] = feed
                logger.info(f"Initialized {exchange_name} feed with {len(exchange_config.instruments)} instruments")
                
            except Exception as e:
                logger.error(f"Failed to initialize {exchange_name} feed: {e}")
    
    async def start(self) -> bool:
        """Start all feeds."""
        if self._running:
            logger.warning("Feed manager is already running")
            return True
        
        if not self.feeds:
            logger.error("No feeds to start")
            return False
        
        logger.info("Starting feed manager...")
        
        # Start all feeds
        started_feeds = []
        for name, feed in self.feeds.items():
            try:
                if await feed.start():
                    started_feeds.append(name)
                    logger.info(f"Started {name} feed")
                else:
                    logger.error(f"Failed to start {name} feed")
            except Exception as e:
                logger.error(f"Error starting {name} feed: {e}")
        
        if not started_feeds:
            logger.error("No feeds started successfully")
            return False
        
        self._running = True
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info(f"Feed manager started with {len(started_feeds)} active feeds: {', '.join(started_feeds)}")
        return True
    
    async def stop(self) -> None:
        """Stop all feeds and cleanup."""
        if not self._running:
            return
        
        logger.info("Stopping feed manager...")
        self._running = False
        
        # Stop cleanup task
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Stop all feeds
        stop_tasks = []
        for name, feed in self.feeds.items():
            if feed.is_running:
                stop_tasks.append(self._stop_feed(name, feed))
        
        if stop_tasks:
            await asyncio.gather(*stop_tasks, return_exceptions=True)
        
        # Disconnect from TimescaleDB
        if self.timescaledb_client:
            await self.timescaledb_client.disconnect()
        
        logger.info("Feed manager stopped")
    
    async def _stop_feed(self, name: str, feed: BaseFeed) -> None:
        """Stop a single feed."""
        try:
            await feed.stop()
            logger.info(f"Stopped {name} feed")
        except Exception as e:
            logger.error(f"Error stopping {name} feed: {e}")
    
    async def _cleanup_loop(self) -> None:
        """Periodic cleanup task."""
        logger.info("Started cleanup loop")
        
        while self._running:
            try:
                # Wait for cleanup interval (every 10 minutes)
                await asyncio.sleep(600)
                
                if not self._running:
                    break
                
                # Cleanup expired data (TimescaleDB handles this automatically)
                if self.timescaledb_client:
                    cleaned = await self.timescaledb_client.cleanup_expired_data()
                    if cleaned > 0:
                        logger.info(f"Cleaned up {cleaned} expired data entries")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def get_status(self) -> Dict[str, any]:
        """Get status of all feeds."""
        status = {
            'running': self._running,
            'feeds': {},
            'timescaledb_connected': self.timescaledb_client._connected if self.timescaledb_client else False
        }
        
        for name, feed in self.feeds.items():
            status['feeds'][name] = {
                'running': feed.is_running,
                'enabled': feed.enabled,
                'instruments': len(feed.instruments)
            }
        
        return status
    
    async def get_latest_funding_rates(self, exchange: Optional[str] = None) -> Dict[str, Dict[str, any]]:
        """Get latest funding rates from TimescaleDB."""
        if not self.timescaledb_client:
            return {}

        return await self.timescaledb_client.get_all_funding_rates(exchange)
    
    def setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run_forever(self) -> None:
        """Run the feed manager indefinitely."""
        try:
            if not await self.initialize():
                logger.error("Failed to initialize feed manager")
                return
            
            if not await self.start():
                logger.error("Failed to start feeds")
                return
            
            # Setup signal handlers
            self.setup_signal_handlers()
            
            logger.info("Feed manager running. Press Ctrl+C to stop.")
            
            # Keep running until stopped
            while self._running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
        finally:
            await self.stop()
