"""Blofin funding rate data feed."""

import asyncio
from typing import Dict, Any, Optional
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from base_feed import Base<PERSON>eed
from config import BlofinConfig
from keydb_client import KeyDBClient
from blofin.websocket_client import BlofinWsPublicClient


class BlofinFeed(BaseFeed):
    """Blofin WebSocket funding rate feed."""
    
    def __init__(self, config: BlofinConfig, keydb_client: KeyDBClient):
        super().__init__(config, keydb_client)
        self.config: BlofinConfig = config
        self.client: Optional[BlofinWsPublicClient] = None
        self._funding_data: Dict[str, Dict[str, Any]] = {}
        self._connected = False
    
    async def connect(self) -> bool:
        """Connect to Blofin WebSocket."""
        try:
            self.client = BlofinWsPublicClient(isDemo=self.config.is_demo)
            success = await self.client.connect()
            
            if success:
                self._connected = True
                self.logger.info("Connected to Blofin WebSocket")
                
                # Subscribe to funding rates for all instruments
                await self._subscribe_to_funding_rates()
                return True
            else:
                self.logger.error("Failed to connect to Blofin WebSocket")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to Blofin: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from Blofin WebSocket."""
        try:
            if self.client:
                await self.client.close()
                self._connected = False
                self.logger.info("Disconnected from Blofin WebSocket")
        except Exception as e:
            self.logger.error(f"Error disconnecting from Blofin: {e}")
    
    async def _subscribe_to_funding_rates(self) -> bool:
        """Subscribe to funding rates for all configured instruments."""
        if not self.client or not self._connected:
            return False
        
        success_count = 0
        for instrument in self.instruments:
            try:
                success = await self.client.subscribeFundingRate(instrument)
                if success:
                    success_count += 1
                    self.logger.info(f"Subscribed to funding rate for {instrument}")
                else:
                    self.logger.error(f"Failed to subscribe to funding rate for {instrument}")
            except Exception as e:
                self.logger.error(f"Error subscribing to {instrument}: {e}")
        
        return success_count == len(self.instruments)
    
    async def fetch_funding_rates(self) -> Dict[str, Dict[str, Any]]:
        """
        Fetch funding rates by listening to WebSocket messages.
        This method processes incoming messages and returns current data.
        """
        if not self.client or not self._connected:
            return {}
        
        try:
            # Process any pending messages
            await self._process_messages()
            
            # Return current funding data
            return self._funding_data.copy()
            
        except Exception as e:
            self.logger.error(f"Error fetching funding rates: {e}")
            return {}
    
    async def _process_messages(self) -> None:
        """Process incoming WebSocket messages."""
        if not self.client:
            return
        
        try:
            # Use asyncio.wait_for to avoid blocking indefinitely
            async for message in self.client.listen():
                processed_data = self._process_funding_rate_message(message)
                if processed_data:
                    # Update internal funding data
                    for update in processed_data["updates"]:
                        instrument = update["instrument"]
                        self._funding_data[instrument] = update
                
                # Break after processing one batch to allow other operations
                break
                
        except asyncio.TimeoutError:
            # No new messages, continue
            pass
        except Exception as e:
            self.logger.error(f"Error processing messages: {e}")
    
    def _process_funding_rate_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process a funding rate message from Blofin WebSocket.
        
        Args:
            message: Raw WebSocket message
            
        Returns:
            Processed funding rate data or None
        """
        try:
            # Check if this is a funding rate message
            arg = message.get("arg", {})
            if arg.get("channel") != "funding-rate":
                return None
            
            data_list = message.get("data", [])
            if not data_list:
                return None
            
            # Process each funding rate update
            processed_data = []
            for data in data_list:
                inst_id = data.get("instId")
                funding_rate = data.get("fundingRate")
                funding_time = data.get("fundingTime")
                
                if inst_id and funding_rate is not None:
                    # Create standardized funding data
                    funding_data = self.create_funding_data(
                        instrument=inst_id,
                        funding_rate=float(funding_rate),
                        funding_time=int(funding_time) if funding_time else None,
                        additional_data={
                            "timestamp": data.get("ts"),
                            "raw_data": data
                        }
                    )
                    processed_data.append(funding_data)
                    
                    self.logger.info(f"Processed funding rate - {inst_id}: {funding_rate}")
            
            return {
                "channel": "funding-rate",
                "updates": processed_data,
                "message_timestamp": message.get("ts")
            }
            
        except Exception as e:
            self.logger.error(f"Error processing funding rate message: {e}")
            return None
    
    async def _run_loop(self) -> None:
        """
        Override the base run loop for WebSocket-based feeds.
        For Blofin, we continuously listen to WebSocket messages.
        """
        self.logger.info(f"Started WebSocket listener for {self.name}")
        
        while self._running:
            try:
                if not self.client or not self._connected:
                    self.logger.warning("WebSocket not connected, attempting reconnect...")
                    if not await self.connect():
                        await asyncio.sleep(5)  # Wait before retry
                        continue
                
                # Listen for messages and process them
                async for message in self.client.listen():
                    if not self._running:
                        break
                    
                    processed_data = self._process_funding_rate_message(message)
                    if processed_data:
                        # Store each update in KeyDB
                        for update in processed_data["updates"]:
                            await self._store_funding_rate(
                                update["instrument"], 
                                update
                            )
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in WebSocket loop: {e}")
                # Attempt to reconnect
                await self.disconnect()
                await asyncio.sleep(5)
    
    def normalize_instrument(self, instrument: str) -> str:
        """Normalize Blofin instrument names."""
        return instrument.upper().replace("-", "")  # BTC-USDT -> BTCUSDT
