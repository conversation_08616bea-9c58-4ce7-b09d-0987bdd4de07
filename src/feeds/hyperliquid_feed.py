"""Hyperliquid funding rate data feed."""

import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from base_feed import BaseFeed
from config import HyperliquidConfig
from keydb_client import KeyDBClient


class HyperliquidFeed(BaseFeed):
    """Hyperliquid REST API funding rate feed."""
    
    def __init__(self, config: HyperliquidConfig, keydb_client: KeyDBClient):
        super().__init__(config, keydb_client)
        self.config: HyperliquidConfig = config
        self.session: Optional[aiohttp.ClientSession] = None
        self._connected = False
    
    async def connect(self) -> bool:
        """Connect to Hyperliquid API (create HTTP session)."""
        try:
            self.session = aiohttp.ClientSession()
            
            # Test connection with a simple request
            await self._test_connection()
            
            self._connected = True
            self.logger.info("Connected to Hyperliquid API")
            return True
            
        except Exception as e:
            self.logger.error(f"Error connecting to Hyperliquid: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from Hyperliquid API (close HTTP session)."""
        try:
            if self.session:
                await self.session.close()
                self._connected = False
                self.logger.info("Disconnected from Hyperliquid API")
        except Exception as e:
            self.logger.error(f"Error disconnecting from Hyperliquid: {e}")
    
    async def _test_connection(self) -> bool:
        """Test connection to Hyperliquid API."""
        if not self.session:
            return False
        
        try:
            async with self.session.post(
                self.config.api_url,
                json={'type': 'metaAndAssetCtxs'},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    return True
                else:
                    self.logger.error(f"API test failed with status {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"API test connection failed: {e}")
            return False
    
    async def fetch_funding_rates(self) -> Dict[str, Dict[str, Any]]:
        """
        Fetch funding rates for all configured instruments from Hyperliquid API.
        
        Returns:
            Dictionary mapping instrument -> funding rate data
        """
        if not self.session or not self._connected:
            return {}
        
        try:
            # Fetch market data from Hyperliquid
            async with self.session.post(
                self.config.api_url,
                json={'type': 'metaAndAssetCtxs'},
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status != 200:
                    self.logger.error(f"API request failed with status {response.status}")
                    return {}
                
                data = await response.json()
                return self._process_api_response(data)
                
        except asyncio.TimeoutError:
            self.logger.error("Timeout fetching data from Hyperliquid API")
            return {}
        except Exception as e:
            self.logger.error(f"Error fetching funding rates: {e}")
            return {}
    
    def _process_api_response(self, data: List[Any]) -> Dict[str, Dict[str, Any]]:
        """
        Process Hyperliquid API response and extract funding rates.
        
        Args:
            data: Raw API response data
            
        Returns:
            Dictionary mapping instrument -> funding rate data
        """
        funding_rates = {}
        
        try:
            if len(data) < 2:
                self.logger.error("Invalid API response format")
                return {}
            
            universe_list = data[0].get('universe', [])
            contexts_list = data[1]
            
            # Process each instrument
            for info, context in zip(universe_list, contexts_list):
                instrument_name = info.get('name', '')
                
                # Filter for configured instruments
                if not self._should_include_instrument(instrument_name):
                    continue
                
                funding_rate = context.get('funding')
                if funding_rate is None:
                    continue
                
                try:
                    funding_rate_float = float(funding_rate)
                except (ValueError, TypeError):
                    self.logger.warning(f"Invalid funding rate for {instrument_name}: {funding_rate}")
                    continue
                
                # Create standardized funding data
                funding_data = self.create_funding_data(
                    instrument=instrument_name,
                    funding_rate=funding_rate_float,
                    additional_data={
                        'open_interest': context.get('openInterest'),
                        'prev_day_px': context.get('prevDayPx'),
                        'day_ntl_vlm': context.get('dayNtlVlm'),
                        'premium': context.get('premium'),
                        'oracle_px': context.get('oraclePx'),
                        'mark_px': context.get('markPx'),
                        'mid_px': context.get('midPx'),
                        'day_base_vlm': context.get('dayBaseVlm'),
                        'raw_data': context
                    }
                )
                
                funding_rates[instrument_name] = funding_data
                self.logger.debug(f"Processed funding rate - {instrument_name}: {funding_rate_float}")
            
            self.logger.info(f"Processed {len(funding_rates)} funding rates from Hyperliquid")
            return funding_rates
            
        except Exception as e:
            self.logger.error(f"Error processing API response: {e}")
            return {}
    
    def _should_include_instrument(self, instrument_name: str) -> bool:
        """
        Check if an instrument should be included based on configuration.
        
        Args:
            instrument_name: Name of the instrument
            
        Returns:
            True if instrument should be included
        """
        if not self.instruments:
            # If no specific instruments configured, include all
            return True
        
        # Check if instrument is in configured list (case-insensitive)
        normalized_name = instrument_name.upper()
        configured_instruments = [inst.upper() for inst in self.instruments]
        
        return normalized_name in configured_instruments
    
    def normalize_instrument(self, instrument: str) -> str:
        """Normalize Hyperliquid instrument names."""
        # Hyperliquid uses simple names like 'BTC', 'ETH'
        return instrument.upper()
    
    async def _run_loop(self) -> None:
        """
        Override the base run loop for REST API-based feeds.
        For Hyperliquid, we poll the API at regular intervals.
        """
        self.logger.info(f"Started polling loop for {self.name}")
        
        while self._running:
            try:
                if not self.session or not self._connected:
                    self.logger.warning("API not connected, attempting reconnect...")
                    if not await self.connect():
                        await asyncio.sleep(10)  # Wait before retry
                        continue
                
                # Fetch funding rates
                funding_rates = await self.fetch_funding_rates()
                
                # Store in KeyDB
                for instrument, data in funding_rates.items():
                    await self._store_funding_rate(instrument, data)
                
                # Wait for next update
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in polling loop: {e}")
                await asyncio.sleep(self.update_interval)
