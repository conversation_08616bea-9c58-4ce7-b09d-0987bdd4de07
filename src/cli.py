"""CLI tool for monitoring funding rate data feeds."""

import asyncio
import json
import sys
from typing import Optional
from keydb_client import KeyDBClient
from config import Config


async def show_status(keydb_client: KeyDBClient) -> None:
    """Show current status of all funding rates."""
    try:
        funding_rates = await keydb_client.get_all_funding_rates()
        
        if not funding_rates:
            print("No funding rate data found.")
            return
        
        print(f"\n{'Exchange':<12} {'Instrument':<12} {'Funding Rate':<15} {'Last Update':<20}")
        print("-" * 70)
        
        for key, data in funding_rates.items():
            exchange = data.get('exchange', 'Unknown')
            instrument = data.get('instrument', 'Unknown')
            funding_rate = data.get('funding_rate', 0)
            timestamp = data.get('collection_time', 0)
            
            # Format timestamp
            import datetime
            dt = datetime.datetime.fromtimestamp(timestamp)
            time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"{exchange:<12} {instrument:<12} {funding_rate:<15.8f} {time_str:<20}")
        
        print(f"\nTotal: {len(funding_rates)} funding rates")
        
    except Exception as e:
        print(f"Error retrieving funding rates: {e}")


async def show_exchange_data(keydb_client: KeyDBClient, exchange: str) -> None:
    """Show funding rates for a specific exchange."""
    try:
        funding_rates = await keydb_client.get_all_funding_rates(exchange)
        
        if not funding_rates:
            print(f"No funding rate data found for {exchange}.")
            return
        
        print(f"\nFunding rates for {exchange.upper()}:")
        print(f"{'Instrument':<15} {'Funding Rate':<15} {'Last Update':<20}")
        print("-" * 55)
        
        for key, data in funding_rates.items():
            instrument = data.get('instrument', 'Unknown')
            funding_rate = data.get('funding_rate', 0)
            timestamp = data.get('collection_time', 0)
            
            # Format timestamp
            import datetime
            dt = datetime.datetime.fromtimestamp(timestamp)
            time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"{instrument:<15} {funding_rate:<15.8f} {time_str:<20}")
        
        print(f"\nTotal: {len(funding_rates)} instruments")
        
    except Exception as e:
        print(f"Error retrieving funding rates for {exchange}: {e}")


async def show_instrument_data(keydb_client: KeyDBClient, exchange: str, instrument: str) -> None:
    """Show detailed data for a specific instrument."""
    try:
        data = await keydb_client.get_funding_rate(exchange, instrument)
        
        if not data:
            print(f"No data found for {exchange}:{instrument}")
            return
        
        print(f"\nDetailed data for {exchange.upper()}:{instrument}:")
        print(json.dumps(data, indent=2, default=str))
        
    except Exception as e:
        print(f"Error retrieving data for {exchange}:{instrument}: {e}")


async def monitor_feeds(keydb_client: KeyDBClient, interval: int = 5) -> None:
    """Monitor feeds in real-time."""
    print(f"Monitoring funding rates (updating every {interval} seconds). Press Ctrl+C to stop.")
    
    try:
        while True:
            # Clear screen
            import os
            os.system('clear' if os.name == 'posix' else 'cls')
            
            print("=== FUNDING RATE MONITOR ===")
            await show_status(keydb_client)
            
            await asyncio.sleep(interval)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")


def print_usage():
    """Print usage information."""
    print("""
Usage: python cli.py <command> [options]

Commands:
    status                          Show all current funding rates
    exchange <name>                 Show funding rates for specific exchange
    instrument <exchange> <symbol>  Show detailed data for specific instrument
    monitor [interval]              Monitor feeds in real-time (default: 5s)
    
Examples:
    python cli.py status
    python cli.py exchange blofin
    python cli.py instrument hyperliquid BTC
    python cli.py monitor 10
    """)


async def main():
    """Main CLI function."""
    if len(sys.argv) < 2:
        print_usage()
        return 1
    
    command = sys.argv[1].lower()
    
    # Load config and connect to KeyDB
    config_manager = Config()
    config = config_manager.load()
    
    keydb_client = KeyDBClient(config.keydb)
    if not await keydb_client.connect():
        print("Failed to connect to KeyDB")
        return 1
    
    try:
        if command == "status":
            await show_status(keydb_client)
            
        elif command == "exchange":
            if len(sys.argv) < 3:
                print("Error: Exchange name required")
                print("Usage: python cli.py exchange <name>")
                return 1
            exchange = sys.argv[2]
            await show_exchange_data(keydb_client, exchange)
            
        elif command == "instrument":
            if len(sys.argv) < 4:
                print("Error: Exchange and instrument required")
                print("Usage: python cli.py instrument <exchange> <symbol>")
                return 1
            exchange = sys.argv[2]
            instrument = sys.argv[3]
            await show_instrument_data(keydb_client, exchange, instrument)
            
        elif command == "monitor":
            interval = 5
            if len(sys.argv) >= 3:
                try:
                    interval = int(sys.argv[2])
                except ValueError:
                    print("Error: Invalid interval value")
                    return 1
            await monitor_feeds(keydb_client, interval)
            
        else:
            print(f"Unknown command: {command}")
            print_usage()
            return 1
            
    finally:
        await keydb_client.disconnect()
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
