# Connect to TimescaleDB

import asyncio
import sys
import os
import json
from typing import defaultdict
from datetime import datetime
import requests

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from timescaledb_client import TimescaleDBClient
from config import TimescaleDBConfig

"""
b'funding:hyperliquid:MEME'
b'{"exchange": "hyperliquid", "instrument": "MEME", "funding_rate": 1.25e-05, "funding_time": null, "collection_time": 772518.695056978, "open_interest": "695190286.0", "prev_day_px": "0.002532", "day_ntl_vlm": "14905746.9010529984", "premium": "0.0002746498", "oracle_px": "0.003641", "mark_px": "0.00364", "mid_px": "0.003643", "day_base_vlm": "3990539058.0", "raw_data": {"funding": "0.0000125", "openInterest": "695190286.0", "prevDayPx": "0.002532", "dayNtlVlm": "14905746.9010529984", "premium": "0.0002746498", "oraclePx": "0.003641", "markPx": "0.00364", "midPx": "0.003643", "impactPxs": ["0.003642", "0.003645"], "dayBaseVlm": "3990539058.0"}, "timestamp": 1755960778.1870492}'

b'funding:blofin:WIFUSDT'
b'{"exchange": "blofin", "instrument": "WIFUSDT", "funding_rate": 0.0002658594884819574, "funding_time": 1755964800000, "collection_time": 772512.131948911, "timestamp": null, "raw_data": {"instId": "WIF-USDT", "fundingRate": "0.0002658594884819574", "fundingTime": "1755964800000"}}'"""
BF_FUNDING_TIME_HOURS_MEMORY = {}
BF_FUNDING_TIME_API = 'https://blofin.com/uapi/v1/basic/contract/info?symbol='

def get_funding_time_hours(pair: str) -> int:
    if pair in BF_FUNDING_TIME_HOURS_MEMORY:
        return BF_FUNDING_TIME_HOURS_MEMORY[pair]
    
    print(f"Getting blofin funding interval of {pair}")
    response = requests.get(BF_FUNDING_TIME_API + pair.replace('USDT', '-USDT'))
    response.raise_for_status()
    response_data = response.json()
    
    funding_interval = response_data['data']['funding_interval']
    BF_FUNDING_TIME_HOURS_MEMORY[pair] = funding_interval

    return BF_FUNDING_TIME_HOURS_MEMORY[pair]

async def get_opportunities():
    # Connect to TimescaleDB and get latest funding rates
    config = TimescaleDBConfig()
    client = TimescaleDBClient(config)

    if not await client.connect():
        print("Failed to connect to TimescaleDB")
        return

    try:
        # Get all latest funding rates
        all_rates = await client.get_all_funding_rates()

        # Based on HL & Blofin data, find best arbitrage opportunities
        funding_data = defaultdict(dict)

        for key, value_json in all_rates.items():
            exchange, ticker = key.split(':', 1)

            if exchange == 'hyperliquid':
                funding_data[ticker]['hl'] = value_json['funding_rate']

            elif exchange == 'blofin':
                funding_hours = get_funding_time_hours(ticker)

                ticker = ticker.replace('USDT', '')
                funding_data[ticker]['bf'] = value_json['funding_rate'] / funding_hours  # We want the hourly rate

        unsorted = []
        for ticker, data in funding_data.items():
            if data.get('hl') and data.get('bf'):
                unsorted.append((ticker, abs(data['hl'] - data['bf'])))

        # Print system time
        print(f"\n\nLatest opportunities as of {datetime.now()}\n")
        sorted_list = sorted(unsorted, key=lambda x: x[1], reverse=True)
        for ticker, diff in sorted_list[:10]:
            # Maker fee = 0.01715% (avg of HL and Blofin)
            prct_arb = round(diff * 100, 5)

            hl_rate = funding_data[ticker]['hl']
            bf_rate = funding_data[ticker]['bf']

            if hl_rate < 0 and bf_rate < 0:
                farm = 'Hyperliquid' if hl_rate < bf_rate else 'Blofin'

            elif hl_rate > 0 and bf_rate > 0:
                farm = 'Hyperliquid' if hl_rate > bf_rate else 'Blofin'

            else:
                farm = 'Both'

            pretty_hl_rate = round(hl_rate * 100, 6)
            pretty_bf_rate = round(bf_rate * 100, 6)

            print(f"{ticker}: {prct_arb}%. HL: {pretty_hl_rate}%, BF: {pretty_bf_rate}% Farming: {farm}")

    except Exception as e:
        print(f"Error getting opportunities: {e}")

    finally:
        await client.disconnect()


async def main():
    try:
        while True:
                await get_opportunities()
                await asyncio.sleep(60)
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == '__main__':
    asyncio.run(main())
