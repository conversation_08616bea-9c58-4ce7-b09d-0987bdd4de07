"""KeyDB client for storing funding rate data."""

import json
import logging
import time
from typing import Dict, Any, Optional, List
import redis.asyncio as redis
from config import KeyDBConfig


logger = logging.getLogger(__name__)


class KeyDBClient:
    """Async KeyDB client for funding rate data storage."""
    
    def __init__(self, config: KeyDBConfig):
        self.config = config
        self.client: Optional[redis.Redis] = None
        self._connected = False
    
    async def connect(self) -> bool:
        """Connect to KeyDB."""
        try:
            self.client = redis.Redis(
                host=self.config.host,
                port=self.config.port,
                db=self.config.db,
                password=self.config.password,
                decode_responses=self.config.decode_responses
            )
            
            # Test connection
            await self.client.ping()
            self._connected = True
            logger.info(f"Connected to KeyDB at {self.config.host}:{self.config.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to KeyDB: {e}")
            self._connected = False
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from KeyDB."""
        if self.client:
            await self.client.close()
            self._connected = False
            logger.info("Disconnected from KeyDB")
    
    async def store_funding_rate(self, exchange: str, instrument: str, data: Dict[str, Any]) -> bool:
        """
        Store funding rate data for an instrument.
        
        Args:
            exchange: Exchange name (e.g., 'blofin', 'hyperliquid')
            instrument: Instrument symbol (e.g., 'BTC-USDT', 'BTC')
            data: Funding rate data dictionary
            
        Returns:
            True if stored successfully
        """
        if not self._connected or not self.client:
            logger.error("Not connected to KeyDB")
            return False
        
        try:
            # Add timestamp if not present
            if 'timestamp' not in data:
                data['timestamp'] = time.time()
            
            # Store latest data
            key = f"funding:{exchange}:{instrument}"
            value = json.dumps(data)
            
            await self.client.set(key, value)
            
            # Set expiration for data cleanup
            await self.client.expire(key, 60)  # 1 min TTL
            
            logger.debug(f"Stored funding rate for {exchange}:{instrument}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store funding rate for {exchange}:{instrument}: {e}")
            return False
    
    async def get_funding_rate(self, exchange: str, instrument: str) -> Optional[Dict[str, Any]]:
        """
        Get latest funding rate data for an instrument.
        
        Args:
            exchange: Exchange name
            instrument: Instrument symbol
            
        Returns:
            Funding rate data or None if not found
        """
        if not self._connected or not self.client:
            logger.error("Not connected to KeyDB")
            return None
        
        try:
            key = f"funding:{exchange}:{instrument}"
            value = await self.client.get(key)
            
            if value:
                return json.loads(value)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get funding rate for {exchange}:{instrument}: {e}")
            return None
    
    async def get_all_funding_rates(self, exchange: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Get all funding rates, optionally filtered by exchange.
        
        Args:
            exchange: Optional exchange filter
            
        Returns:
            Dictionary of funding rates keyed by exchange:instrument
        """
        if not self._connected or not self.client:
            logger.error("Not connected to KeyDB")
            return {}
        
        try:
            pattern = f"funding:{exchange}:*" if exchange else "funding:*"
            keys = await self.client.keys(pattern)
            
            result = {}
            for key in keys:
                value = await self.client.get(key)
                if value:
                    # Extract exchange and instrument from key
                    parts = key.split(':')
                    if len(parts) >= 3:
                        exch = parts[1]
                        inst = ':'.join(parts[2:])  # Handle instruments with colons
                        result[f"{exch}:{inst}"] = json.loads(value)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get all funding rates: {e}")
            return {}
    
    async def cleanup_expired_data(self) -> int:
        """
        Clean up expired funding rate data.
        
        Returns:
            Number of keys cleaned up
        """
        if not self._connected or not self.client:
            return 0
        
        try:
            keys = await self.client.keys("funding:*")
            expired_keys = []
            
            for key in keys:
                ttl = await self.client.ttl(key)
                if ttl == -1:  # No expiration set
                    expired_keys.append(key)
            
            if expired_keys:
                await self.client.delete(*expired_keys)
                logger.info(f"Cleaned up {len(expired_keys)} expired funding rate keys")
            
            return len(expired_keys)
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired data: {e}")
            return 0
