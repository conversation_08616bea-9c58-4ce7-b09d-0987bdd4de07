"""Base data feed class for funding rate collection."""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from timescaledb_client import TimescaleDBClient
from config import ExchangeConfig


logger = logging.getLogger(__name__)


class BaseFeed(ABC):
    """Abstract base class for exchange data feeds."""
    
    def __init__(self, config: ExchangeConfig, timescaledb_client: TimescaleDBClient):
        self.config = config
        self.timescaledb_client = timescaledb_client
        self.name = config.name
        self.instruments = config.instruments
        self.update_interval = config.update_interval
        self.enabled = config.enabled
        
        self._running = False
        self._task: Optional[asyncio.Task] = None
        
        # Setup logger for this feed
        self.logger = logging.getLogger(f"feed.{self.name}")
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to the exchange data source."""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """Disconnect from the exchange data source."""
        pass
    
    @abstractmethod
    async def fetch_funding_rates(self) -> Dict[str, Dict[str, Any]]:
        """
        Fetch funding rates for all configured instruments.
        
        Returns:
            Dictionary mapping instrument -> funding rate data
        """
        pass
    
    async def start(self) -> bool:
        """Start the data feed."""
        if not self.enabled:
            self.logger.info(f"Feed {self.name} is disabled, skipping start")
            return False
        
        if self._running:
            self.logger.warning(f"Feed {self.name} is already running")
            return True
        
        self.logger.info(f"Starting {self.name} data feed")
        
        # Connect to exchange
        if not await self.connect():
            self.logger.error(f"Failed to connect to {self.name}")
            return False
        
        # Start the main loop
        self._running = True
        self._task = asyncio.create_task(self._run_loop())
        
        return True
    
    async def stop(self) -> None:
        """Stop the data feed."""
        if not self._running:
            return
        
        self.logger.info(f"Stopping {self.name} data feed")
        self._running = False
        
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        
        await self.disconnect()
    
    async def _run_loop(self) -> None:
        """Main data collection loop."""
        self.logger.info(f"Started data collection loop for {self.name}")
        
        while self._running:
            try:
                # Fetch funding rates
                funding_rates = await self.fetch_funding_rates()
                
                # Store in KeyDB
                for instrument, data in funding_rates.items():
                    await self._store_funding_rate(instrument, data)
                
                # Wait for next update
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in data collection loop: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def _store_funding_rate(self, instrument: str, data: Dict[str, Any]) -> None:
        """Store funding rate data in TimescaleDB."""
        try:
            success = await self.timescaledb_client.store_funding_rate(
                exchange=self.name,
                instrument=instrument,
                data=data
            )
            
            if success:
                funding_rate = data.get('funding_rate', 'N/A')
                self.logger.debug(f"Stored {instrument}: {funding_rate}")
            else:
                self.logger.warning(f"Failed to store {instrument} funding rate")
                
        except Exception as e:
            self.logger.error(f"Error storing funding rate for {instrument}: {e}")
    
    def normalize_instrument(self, instrument: str) -> str:
        """
        Normalize instrument name for consistent storage.
        Override in subclasses if needed.
        """
        return instrument.upper()
    
    def create_funding_data(self, 
                          instrument: str,
                          funding_rate: float,
                          funding_time: Optional[int] = None,
                          additional_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create standardized funding rate data structure.
        
        Args:
            instrument: Instrument symbol
            funding_rate: Funding rate value
            funding_time: Funding timestamp (optional)
            additional_data: Additional exchange-specific data
            
        Returns:
            Standardized funding rate data
        """
        data = {
            'exchange': self.name,
            'instrument': self.normalize_instrument(instrument),
            'funding_rate': float(funding_rate),
            'funding_time': funding_time,
            'collection_time': asyncio.get_event_loop().time()
        }
        
        if additional_data:
            data.update(additional_data)
        
        return data
    
    @property
    def is_running(self) -> bool:
        """Check if the feed is currently running."""
        return self._running
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.name})"
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.name}', enabled={self.enabled}, instruments={len(self.instruments)})"
