"""Configuration management for funding arbitrage data feeds."""

import json
import os
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field


class KeyDBConfig(BaseModel):
    """KeyDB connection configuration (deprecated - use TimescaleDBConfig)."""
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: Optional[str] = None
    decode_responses: bool = True


class TimescaleDBConfig(BaseModel):
    """TimescaleDB connection configuration."""
    host: str = "localhost"
    port: int = 5432
    user: str = "postgres"
    password: str = "fundingarb"
    database: str = "fundingarb"
    min_connections: int = 1
    max_connections: int = 10
    command_timeout: int = 60


class ExchangeConfig(BaseModel):
    """Base exchange configuration."""
    name: str
    enabled: bool = True
    instruments: List[str] = Field(default_factory=list)
    update_interval: float = 1.0  # seconds


class BlofinConfig(ExchangeConfig):
    """Blofin-specific configuration."""
    name: str = "blofin"
    is_demo: bool = False
    instruments: List[str] = Field(default_factory=lambda: ["BTC-USDT", "ETH-USDT", "SOL-USDT"])


class HyperliquidConfig(ExchangeConfig):
    """Hyperliquid-specific configuration."""
    name: str = "hyperliquid"
    api_url: str = "https://api.hyperliquid.xyz/info"
    instruments: List[str] = Field(default_factory=lambda: ["BTC", "ETH", "SOL"])
    update_interval: float = 5.0  # Hyperliquid updates less frequently


class DataFeedConfig(BaseModel):
    """Main data feed configuration."""
    keydb: KeyDBConfig = Field(default_factory=KeyDBConfig)  # Deprecated
    timescaledb: TimescaleDBConfig = Field(default_factory=TimescaleDBConfig)
    exchanges: Dict[str, Union[BlofinConfig, HyperliquidConfig, ExchangeConfig]] = Field(default_factory=dict)
    log_level: str = "INFO"
    data_retention_seconds: int = 3600  # Keep data for 1 hour (deprecated for TimescaleDB)


class Config:
    """Configuration manager."""
    
    def __init__(self, config_path: str = "config/feeds.json"):
        self.config_path = config_path
        self._config: Optional[DataFeedConfig] = None
    
    def load(self) -> DataFeedConfig:
        """Load configuration from file or create default."""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r') as f:
                data = json.load(f)
                self._config = DataFeedConfig(**data)
        else:
            self._config = self._create_default_config()
            self.save()
        
        return self._config
    
    def save(self) -> None:
        """Save current configuration to file."""
        if self._config is None:
            return
        
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        with open(self.config_path, 'w') as f:
            json.dump(self._config.model_dump(), f, indent=2)
    
    def _create_default_config(self) -> DataFeedConfig:
        """Create default configuration."""
        return DataFeedConfig(
            exchanges={
                "blofin": BlofinConfig(),
                "hyperliquid": HyperliquidConfig()
            }
        )
    
    @property
    def config(self) -> DataFeedConfig:
        """Get current configuration."""
        if self._config is None:
            self.load()
        return self._config
