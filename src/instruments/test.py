import requests

def get_blofin_instruments():
    url = 'https://openapi.blofin.com/api/v1/market/instruments'
    response = requests.get(url)
    response.raise_for_status()
    response_data = response.json()

    tickers = set()
    for data in response_data.get('data', []):
        print(data)
        if data.get('maxLeverage'):
            tickers.add(data['instId'].split('-')[0])
    return tickers

def get_hyperliquid_instruments():
    url = 'https://api.hyperliquid.xyz/info'
    response = requests.post(url, json={'type': 'allMids'})
    response.raise_for_status()
    response_data = response.json()
    tickers = set()
    for k in response_data.keys():
        if k.startswith('@'):
            continue

        tickers.add(k)
    return tickers

if __name__ == '__main__':
    bf_tickers = get_blofin_instruments()
    hl_tickers = get_hyperliquid_instruments()

    common = bf_tickers.intersection(hl_tickers)
    common = [f"{t}" for t in common]
    print(common)
