# Funding Arbitrage Bot - Data Feeds

A modular data feed system for collecting funding rate data from multiple cryptocurrency exchanges and storing it in KeyDB for arbitrage analysis.

## Features

- **Modular Architecture**: Easy to add new exchanges
- **Real-time Data**: WebSocket and REST API support
- **KeyDB Storage**: Fast in-memory storage with persistence
- **Configurable**: JSON-based configuration
- **Monitoring**: CLI tools for real-time monitoring
- **Robust**: Error handling and automatic reconnection

## Supported Exchanges

- **Blofin**: WebSocket-based real-time funding rates
- **Hyperliquid**: REST API polling for funding rates

## Quick Start

### Prerequisites

1. **KeyDB**: Running on Docker
   ```bash
   docker run -d --name keydb -p 6379:6379 eqalpha/keydb
   ```

2. **Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Configuration

Edit `config/feeds.json` to configure exchanges and instruments:

```json
{
  "keydb": {
    "host": "localhost",
    "port": 6379
  },
  "exchanges": {
    "blofin": {
      "enabled": true,
      "instruments": ["BTC-USDT", "ETH-USDT", "SOL-USDT"]
    },
    "hyperliquid": {
      "enabled": true,
      "instruments": ["BTC", "ETH", "SOL"]
    }
  }
}
```

### Running the Data Feeds

```bash
# Start all configured data feeds
python main.py
```

### Monitoring

```bash
# Show current funding rates
python src/cli.py status

# Monitor specific exchange
python src/cli.py exchange blofin

# Real-time monitoring
python src/cli.py monitor

# Detailed instrument data
python src/cli.py instrument hyperliquid BTC
```

## Architecture

### Core Components

- **FeedManager**: Coordinates all data feeds
- **BaseFeed**: Abstract base class for exchange feeds
- **KeyDBClient**: Handles data storage and retrieval
- **Config**: Configuration management

### Data Flow

1. **Exchange Feeds** collect funding rate data
2. **Data Normalization** standardizes format across exchanges
3. **KeyDB Storage** stores latest data with TTL
4. **CLI Tools** provide monitoring and analysis

### Adding New Exchanges

1. Create new feed class inheriting from `BaseFeed`
2. Implement required methods: `connect()`, `disconnect()`, `fetch_funding_rates()`
3. Add to `FEED_CLASSES` registry in `FeedManager`
4. Update configuration

## Data Format

Funding rate data is stored in KeyDB with the following structure:

```json
{
  "exchange": "blofin",
  "instrument": "BTCUSDT",
  "funding_rate": 0.0000125,
  "funding_time": 1703123456,
  "collection_time": 1703123456.789,
  "additional_data": {...}
}
```

## KeyDB Schema

- **Key Pattern**: `funding:{exchange}:{instrument}`
- **TTL**: 1 hour (configurable)
- **Format**: JSON strings

## Logging

Logs are written to:
- Console (INFO level)
- `logs/feeds.log` (configurable level)

## Error Handling

- Automatic reconnection on connection failures
- Graceful degradation when exchanges are unavailable
- Comprehensive error logging
- Signal handling for clean shutdown